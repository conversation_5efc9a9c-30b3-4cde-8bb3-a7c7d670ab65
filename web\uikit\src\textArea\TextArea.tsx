import React, { useState, useEffect, useRef } from "react";
import styles from "./textarea.module.css";
import { defaultProps } from "~/default.spec";
import { useFocusedBy } from "~/utils";
import { Icon } from "~/icon";

interface TextAreaProps extends defaultProps {
  label?: string;
  placeholder?: string;
  maxLength?: number;
  displayMaxLength?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  validationState?: "error" | "success";
  errorMessage?: string;
  hideLabel?: boolean;
  required?: boolean;
  maxLengthTopPosition?: number;
}

export const TextArea: React.FC<TextAreaProps> = ({
  label = "Text Area",
  placeholder = "What needs to be done?",
  maxLength = 200,
  displayMaxLength = false,
  value = "",
  onChange,
  validationState,
  errorMessage,
  hideLabel = true,
  dataTestId = "uikit-textArea",
  role,
  ariaLabel = "Text Area",
  ariaDescribedBy,
  ariaLabelledBy,
  tabIndex,
  required,
  maxLengthTopPosition = 0
}) => {
  const [text, setText] = useState(value);
  const ref = useRef<HTMLTextAreaElement>(null);
  const { focusClass } = useFocusedBy();

  useEffect(() => {
    setText(value);
  }, [value]);

  useEffect(() => {
    if (ref.current) {
      ref.current.style.height = `${ref.current.scrollHeight}px`;
    }
  }, [ref, ref.current]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    if (newValue.length <= maxLength) {
      setText(newValue);
      if (onChange) onChange(newValue);
    }
  };

  const textAreaClass = `${styles.textArea} ${focusClass} ${validationState === "error"
    ? styles.error
    : validationState === "success"
      ? styles.success
      : ""
    }`;

  const resizeTimeout = useRef<ReturnType<typeof setTimeout>>();

  const showInfo = !hideLabel || displayMaxLength;

  return (
    <div
      className={styles.container}
      data-testid={dataTestId}
      role={role}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
      aria-labelledby={ariaLabelledBy}
      tabIndex={tabIndex}
    >
      {showInfo && (
        <div className={styles.info}>
          <label
            htmlFor="textarea"
            className={`${hideLabel ? styles.hiddenLabel : styles.label}`}
          >
            {label}
          </label>
          {required && <span className={styles.requiredStar}>*</span>}

          {displayMaxLength && (
            <span className={styles.maxLength} style={{ top: `${maxLengthTopPosition}px` }}>
              {text.length} / {maxLength}
            </span>
          )}
        </div>
      )}

      <textarea
        required={required}
        id="textarea"
        ref={ref}
        className={textAreaClass}
        placeholder={placeholder}
        value={text}
        onChange={e => {
          const el = e.currentTarget;
          clearTimeout(resizeTimeout.current);
          resizeTimeout.current = setTimeout(() => {
            el.style.height = "auto";
            el.style.height = `${el.scrollHeight}px`;
          }, 0);

          handleChange(e);
        }}
        rows={1}
        aria-label={label}
      />

      {validationState === "error" && errorMessage && (
        <div className={styles.info}>
          <Icon iconName="error-message-icon" size={20} />
          <span id="error-message" className={styles.errorMessage}>
            {errorMessage}
          </span>
        </div>
      )}
    </div>
  );
};
