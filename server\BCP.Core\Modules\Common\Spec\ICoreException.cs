namespace BCP.Core.Common;

public enum CoreError
{
    // Build in client errors (4xx)
    BadRequest,
    Unauthorized,
    Forbidden,
    NotFound,
    Conflict,
    ValidationError,
    UnprocessableEntity,
    TooManyRequests,

    // Build in server errors (5xx)
    InternalError,
    ServiceUnavailable,
    BadGateway,
    GatewayTimeout,

    // Custom Business logic errors
    BusinessRuleViolation,
    ResourceAlreadyExists,
    ResourceInUse,
    InvalidOperation,
    ExternalServiceError,
}

public interface ICoreException
{
    CoreError Code { get; }
    int HttpCode { get; }
    string Message { get; }
    object? ExData { get; }
}
