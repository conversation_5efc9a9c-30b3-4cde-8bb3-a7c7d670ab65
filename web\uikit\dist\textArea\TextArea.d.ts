import React from "react";
import { defaultProps } from "~/default.spec";
interface TextAreaProps extends defaultProps {
    label?: string;
    placeholder?: string;
    maxLength?: number;
    displayMaxLength?: boolean;
    value?: string;
    onChange?: (value: string) => void;
    validationState?: "error" | "success";
    errorMessage?: string;
    hideLabel?: boolean;
    required?: boolean;
    maxLengthTopPosition?: number;
}
export declare const TextArea: React.FC<TextAreaProps>;
export {};
