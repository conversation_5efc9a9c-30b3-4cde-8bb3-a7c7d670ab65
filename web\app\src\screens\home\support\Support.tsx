import {
  <PERSON>ton,
  ButtonSizeEnum,
  ButtonTypeEnum,
  CircleButton,
  DocumentUpload,
  DocumentUploadFile,
  DropdownInput,
  DropdownInputItem,
  Input,
  TextArea,
} from "@bcp/uikit";
import { Icon } from "@bcp/uikit";
import styles from "./support.module.css";
import React, { useState } from "react";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import { useToastService } from "~/services/toast";
import { useSupportService } from "~/services/support";

export const acceptedFileTypes = [
  "application/pdf",
  "image/jpeg",
  "image/png",
  "image/gif",
  "application/zip",
  "application/x-zip-compressed",
  "application/vnd.rar",
  "text/plain",
  "application/msword",
  "application/vnd.ms-excel",
  "application/vnd.ms-powerpoint",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  "application/vnd.openxmlformats-officedocument.presentationml.presentation",
  "application/vnd.ms-excel.sheet.macroEnabled.12",
  "application/vnd.ms-word.document.macroEnabled.12",
  "application/vnd.ms-powerpoint.presentation.macroEnabled.12",
  "application/vnd.visio",
  "application/vnd.ms-visio.drawing.main+xml",
  "application/vnd.visio2013",
  "text/html",
  "application/xml",
  "text/xml",
  "application/xslt+xml",
  "video/mp4",
  "application/x-7z-compressed",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.template",
  "application/vnd.ms-xpsdocument",
  "application/vnd.ms-outlook",
  "message/rfc822",
  "text/csv",
  "application/x-qbw",
  "application/x-preview",
  "application/x-game",
  "application/x-zipx",
];

const timeZoneOptions = [
  { label: "Pacific Time (PT) UTC–08:00", value: "pacific" },
  { label: "Mountain Time (MT) UTC–07:00", value: "mountain" },
  { label: "Central Time (CT) UTC–06:00", value: "central" },
  { label: "Eastern Time (ET) UTC–05:00", value: "eastern" },
].sort((x, y) => x.label.localeCompare(y.label));

const issues = [
  "Sign in Issues",
  "Browser Compatibility",
  "Onboarding",
  "Action items",
  "Document Uploads/File Issues",
  "Project Progress/ Project Timeline",
  "Profile & Notifications",
];

interface supportProps {
  setIsSupport: (isSupport: boolean) => void;
}

const Support: React.FC<supportProps> = ({ setIsSupport }) => {
  const { t } = useTranslation("support");
  const [name, setName] = useState<string>("");
  const [companyName, setCompanyName] = useState<string>("");
  const [email, setEmail] = useState<string>("");
  const [phoneNumber, setPhoneNumber] = useState<string>("");
  const [description, setDescription] = useState<string>("");
  const [contactMethod, setContactMethod] = useState<string>("");
  const [timeZone, setTimeZone] = useState<string>("");
  const [issue, setIssue] = useState<string>("");
  const [files, setFiles] = useState<DocumentUploadFile[]>([]);
  const { showToast } = useToastService();
  const { submit } = useSupportService();

  const issuesOptions = issues
    .map(x => {
      return { label: t(x), value: x };
    })
    .sort((x, y) => x.label.localeCompare(y.label));

  const contactMethods = [
    { label: t("email"), value: "email" },
    { label: t("phone"), value: "phone" },
  ];

  const onSubmit = async () => {
    const result = await submit({
      name,
      companyName,
      email,
      phoneNumber,
      contactMethod,
      timeZone,
      issue,
      description,
      documents: files,
    });
    if (result) {
      setIsSupport(false);
      showToast({
        message: t("send-email-success-message"),
        type: "success",
        persist: false,
      });
    } else {
      showToast({
        title: t('Request submitted'),
        message: t("send-email-failure-message"),
        type: "error",
        persist: false,
      });
    }
  };

  return (
    <div>
      <div className={styles.header}>
        <div className={classNames(styles.pageTitleContainer)}>
          <div className={styles.pageTitleWrapper}>
            <CircleButton
              ariaLabel="Back Button"
              dataTestId="uikit-backButton"
              inputId="back-button"
              icon={
                <Icon
                  iconName="arrow-left-thicker"
                  altText="Dismiss modal button"
                />
              }
              onClick={() => {
                setIsSupport(false);
              }}
            />
            <h2 className={styles.pageTitle}>{t("header")}</h2>
          </div>
        </div>
      </div>
      <div className={styles.formContainer}>
        <div className={styles.twoColumns}>
          <b>{t("form-title")}</b>
        </div>
        <div className={styles.twoColumns}>{t("description")}</div>
        <div className={styles.twoColumns}>
          {t("required-field")}&nbsp;
          <span className={styles.requiredFieldsTextAsterisk}>*</span>
        </div>
        <Input
          inputId={"name"}
          placeholder={t("name") + "*"}
          onValueChange={setName}
          value={name}
          dataTestId="uikit-input-name"
          replaceLineBreaksWithBulletPoints={true}
          error={!name}
          errorMessage={t("required-field-error")}
        ></Input>
        <Input
          inputId={"company-name"}
          placeholder={t("company-name") + "*"}
          onValueChange={setCompanyName}
          value={companyName}
          dataTestId="uikit-input-company-name"
          replaceLineBreaksWithBulletPoints={true}
          error={!companyName}
          errorMessage={t("required-field-error")}
        ></Input>
        <Input
          inputId={"email"}
          placeholder={t("email") + "*"}
          onValueChange={setEmail}
          value={email}
          dataTestId="uikit-email"
          replaceLineBreaksWithBulletPoints={true}
          error={!email}
          errorMessage={t("required-field-error")}
        ></Input>
        <Input
          inputId={"phone-humber"}
          placeholder={t("phone-number") + "*"}
          onValueChange={setPhoneNumber}
          value={phoneNumber}
          dataTestId="uikit-phone-number"
          replaceLineBreaksWithBulletPoints={true}
          error={!phoneNumber}
          errorMessage={t("required-field-error")}
        ></Input>
        <DropdownInput
          ariaLabel={t("preferred-contact-method")}
          items={contactMethods}
          onSelectionChange={(item: DropdownInputItem) => {
            setContactMethod(item.value.toString());
          }}
          enableSearch={false}
          floatingLabelEnabled={false}
          id="preferred-contact-method"
          placeholder={t("preferred-contact-method") + "*"}
          error={!contactMethod}
          errorMessage={t("required-field-error")}
        />
        <DropdownInput
          ariaLabel={t("time-zone")}
          items={timeZoneOptions}
          onSelectionChange={(item: DropdownInputItem) => {
            setTimeZone(item.value.toString());
          }}
          enableSearch={false}
          floatingLabelEnabled={false}
          id="time-zone"
          placeholder={t("time-zone") + "*"}
          error={!timeZone}
          errorMessage={t("required-field-error")}
        />
        <span className={styles.twoColumns}>
          <DropdownInput
            ariaLabel={t("What's the issue?")}
            items={issuesOptions}
            onSelectionChange={(item: DropdownInputItem) => {
              setIssue(item.value.toString());
            }}
            enableSearch={false}
            floatingLabelEnabled={false}
            id="whats-the-issue"
            placeholder={t("What's the issue?") + "*"}
            error={!issue}
            errorMessage={t("required-field-error")}
          />
        </span>
        <span className={styles.twoColumns}>
          <TextArea
            hideLabel={true}
            label={t("tell-us-more") + "*"}
            placeholder={t("tell-us-more") + "*"}
            value={description}
            onChange={e => {
              setDescription(e);
            }}
            maxLength={1000}
            displayMaxLength
            errorMessage={t("required-field-error")}
            maxLengthTopPosition={-15}
          />
        </span>
        <span className={styles.twoColumns}>
          <DocumentUpload
            id="support-documents"
            showDropZone
            title={t("upload-title")}
            subtitle={t("upload-subtitle")}
            acceptedFileTypes={acceptedFileTypes}
            onFileAccept={files => {
              setFiles(files);
            }}
            onFileDelete={files => {
              setFiles(files);
            }}
            existingFiles={files}
            required={false}
          />
        </span>
        <div className={styles.twoColumns}>{t("footer-line-1")}</div>
        <div className={styles.phoneNumber}>
          <u>**************</u>
        </div>
        <div className={styles.twoColumns}>
          <b>{t("client-support-hours")}</b>
        </div>
        <div className={styles.twoColumns}>8:30 AM to 7:00 PM ET</div>
        <div className={styles.twoColumns}>
          {t("monday")}&nbsp;-&nbsp;{t("friday")}
        </div>
      </div>
      {/* 

      <div className={styles.header}>
        <div className={classNames(styles.pageTitleContainer)}>
          <div className={styles.buttonContainer}>
            <Button
              id="submit-button"
              label={t("submit")}
              onClick={onSubmit}
              type={ButtonTypeEnum.secondary}
              size={ButtonSizeEnum.large}
              dataTestId={"submit-button"}
              withRightIcon={false}
              disabled={
                !name ||
                !companyName ||
                !email ||
                !phoneNumber ||
                !description ||
                !contactMethod ||
                !timeZone ||
                !issue
              }
            />
          </div>
        </div>
      </div> */}
  
    </div>
  );
};

export default Support;
