using System.Net;
using System.Net.Http.Headers;
using System.Text.Json;
using BCP.Core.BGP.ControlAPI.Config;
using BCP.Core.BGP.ControlAPI.Spec;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Primitives;

namespace BCP.Core.BGP.ControlAPI
{
    internal class ControlApiClient : IControlApiClient, IDisposable
    {
        private readonly IControlApiConfig _config;
        private readonly HttpClient _httpClient;

        public ControlApiClient(IHttpContextAccessor httpContextAccessor, IControlApiConfig config)
        {
            _config = config;

            var token = httpContextAccessor
                .HttpContext?.Request.Headers["Authorization"]
                .ToString()
                .Replace("Bearer ", "");
            _httpClient = new HttpClient
            {
                BaseAddress = new Uri(_config.ControlAPIBaseUrl, UriKind.RelativeOrAbsolute),
            };
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                "Bearer",
                token
            );
            _httpClient.DefaultRequestHeaders.Accept.Add(
                new MediaTypeWithQualityHeaderValue("application/json")
            );
        }

        public void InitializeWithToken(string token)
        {
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                "Bearer",
                token
            );
        }

        public async Task<T?> GetAsync<T>(
            string endpoint,
            Dictionary<string, StringValues>? queryParams = null
        )
        {
            var uri = BuildUri(endpoint, queryParams);
            return await SendRequestAsync<T>(HttpMethod.Get, uri);
        }

        public async Task<T?> PutAsync<T>(
            string endpoint,
            Dictionary<string, StringValues>? queryParams = null
        )
        {
            var uri = BuildUri(endpoint, queryParams);
            return await SendRequestAsync<T>(HttpMethod.Put, uri);
        }

        public async Task<T?> PostAsync<T>(
            string endpoint,
            object payload,
            Dictionary<string, StringValues>? queryParams = null
        )
        {
            var uri = BuildUri(endpoint, queryParams);
            var jsonContent = new StringContent(
                JsonSerializer.Serialize(payload),
                System.Text.Encoding.UTF8,
                "application/json"
            );

            using var request = new HttpRequestMessage(HttpMethod.Post, uri)
            {
                Content = jsonContent,
            };

            using var response = await _httpClient.SendAsync(request);

            response.EnsureSuccessStatusCode();

            if (response.Content.Headers.ContentLength == 0)
            {
                return default;
            }

            var responseStream = await response.Content.ReadAsStreamAsync();
            // If the response is an empty JSON
            // No need to deserialize
            StreamReader reader = new StreamReader(responseStream);
            if (responseStream == Stream.Null || reader.ReadToEnd() == "{}")
                return default;
            responseStream.Position = 0;
            return await JsonSerializer.DeserializeAsync<T>(
                responseStream,
                new JsonSerializerOptions { PropertyNameCaseInsensitive = true }
            );
        }

        public async Task<T?> DeleteAsync<T>(
            string endpoint,
            object payload,
            Dictionary<string, StringValues>? queryParams = null
        )
        {
            var uri = BuildUri(endpoint, queryParams);
            var jsonContent = new StringContent(
                JsonSerializer.Serialize(payload),
                System.Text.Encoding.UTF8,
                "application/json"
            );

            using var request = new HttpRequestMessage(HttpMethod.Delete, uri)
            {
                Content = jsonContent,
            };
            using var response = await _httpClient.SendAsync(request);

            response.EnsureSuccessStatusCode();

            if (response.Content.Headers.ContentLength == 0)
            {
                return default;
            }

            var responseStream = await response.Content.ReadAsStreamAsync();
            // If the response is an empty JSON
            // No need to deserialize
            StreamReader reader = new StreamReader(responseStream);
            if (responseStream == Stream.Null || reader.ReadToEnd() == "{}")
                return default;
            responseStream.Position = 0;
            return await JsonSerializer.DeserializeAsync<T>(
                responseStream,
                new JsonSerializerOptions { PropertyNameCaseInsensitive = true }
            );
        }

        public async Task<HttpResponseMessage> UploadFileAsync<T>(
            string endpoint,
            HttpContent content
        )
        {
            var response = await _httpClient.PostAsync(endpoint, content);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                throw new HttpRequestException(
                    $"Request failed: {response.StatusCode}. Response: {errorContent}"
                );
            }

            return response;
        }

        private string BuildUri(
            string endpoint,
            Dictionary<string, StringValues>? queryParams = null
        )
        {
            var finalEndpoint = endpoint.TrimStart('/');
            return queryParams != null
                ? QueryHelpers.AddQueryString(finalEndpoint, queryParams!)
                : finalEndpoint;
        }

        private async Task<T?> SendRequestAsync<T>(
            HttpMethod method,
            string uri,
            HttpContent? content = null
        )
        {
            var finalUri = uri.TrimStart('/');
            using var request = new HttpRequestMessage(method, finalUri) { Content = content };

            var response = await _httpClient.SendAsync(request);
            if (response.StatusCode == HttpStatusCode.NoContent)
            {
                return default;
            }
            return await HandleResponse<T>(response);
        }

        private async Task<T?> HandleResponse<T>(HttpResponseMessage response)
        {
            response.EnsureSuccessStatusCode();
            var contentType = response.Content.Headers.ContentType?.MediaType;

            if (contentType == "application/json")
            {
                var responseStream = await response.Content.ReadAsStreamAsync();
                return await JsonSerializer.DeserializeAsync<T>(
                    responseStream,
                    new JsonSerializerOptions { PropertyNameCaseInsensitive = true }
                );
            }
            else if (
                contentType?.StartsWith("application/") == true
                || contentType?.StartsWith("image/") == true
            )
            {
                var byteArray = await response.Content.ReadAsByteArrayAsync();
                var responseRaw = new Dictionary<string, byte[]>();
                responseRaw.Add("imageByteArray", byteArray);
                var serialized = JsonSerializer.Serialize(responseRaw);
                var returnedStream = JsonSerializer.Deserialize<T>(
                    serialized,
                    new JsonSerializerOptions { PropertyNameCaseInsensitive = true }
                );
                return returnedStream;
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                throw new HttpRequestException(
                    $"Unexpected response type: {contentType}. Response: {errorContent}"
                );
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
